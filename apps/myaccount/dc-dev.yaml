version: '3.8'

services:
  api:
    image: keeps-myaccount:latest
    container_name: keeps-myaccount-api
    build:
      context: .
      dockerfile: .docker/dev/Dockerfile
    ports:
      - '4000:4000'
    env_file:
      - ./.env
    volumes:
      - type: bind
        source: ../../
        target: /usr/src/app
    command: ['./apps/myaccount/.docker/dev/start.sh']
    depends_on:
      - redis
    networks:
      - myaccount-network
      - keeps-shared-network

  redis:
    container_name: keeps-myaccount-redis
    image: redis:7.2.1-alpine
    env_file:
      - ./.env
    networks:
      - myaccount-network

  rabbitmq:
    image: 'rabbitmq:3-management'
    container_name: keeps-rabbitmq
    ports:
      - 5672:5672
      - 15672:15672
    env_file:
      - ./.env
    networks:
      - keeps-shared-network

networks:
  myaccount-network:
    driver: bridge
  keeps-shared-network:
    external: true

volumes:
  pgdata:
    driver: local

import { Column, <PERSON>tity, Index, <PERSON>in<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { BaseEntity } from './base-entity';
import { Workspace } from './workspace.entity';

@Index('workspace_custom_menu_item_pkey', ['id'], { unique: true })
@Entity('workspace_custom_menu_item', { schema: 'public' })
export class WorkspaceCustomMenuItem extends BaseEntity {
  @Column('character varying', { name: 'name', length: 200 })
  name: string;

  @Column('character varying', { name: 'icon', length: 500 })
  icon: string;

  @Column('uuid', { name: 'workspace_id' })
  workspaceId: string;

  @ManyToOne(() => Workspace, (workspace) => workspace.customMenuItems)
  @JoinColumn([{ name: 'workspace_id', referencedColumnName: 'id' }])
  workspace: Workspace;
}

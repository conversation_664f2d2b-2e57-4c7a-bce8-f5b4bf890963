import { InvalidEmailException } from '../exceptions/invalid-email.exception';

export function cleanEmail(email?: string): string {
  if (!email) {
    throw new InvalidEmailException();
  }

  const cleanedEmail = email.replace(/[^a-zA-Z0-9@._+-]/g, '');
  const parts = cleanedEmail.split('@');

  if (parts.length !== 2 || !parts[0] || !parts[1]) {
    throw new InvalidEmailException();
  }

  return cleanedEmail;
}

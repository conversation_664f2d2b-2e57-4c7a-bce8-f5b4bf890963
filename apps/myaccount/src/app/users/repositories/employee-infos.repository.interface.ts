import { Repository, SelectQueryBuilder } from 'typeorm';
import { EmployeeInfo } from '../../entities/employee-info.entity';
import { EmployeeInfoListParamsDto } from '../dtos/employee-info-list-params.dto';

export default interface IEmployeeInfosRepository extends Repository<EmployeeInfo> {
  findAllowed(workspaceId: string, filters?: EmployeeInfoListParamsDto): SelectQueryBuilder<EmployeeInfo>;
  findUniqueValues(workspaceId: string, column: keyof EmployeeInfo, search?: string): SelectQueryBuilder<EmployeeInfo>;
}

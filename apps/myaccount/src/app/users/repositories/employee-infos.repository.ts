import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { FilterCondition, Order } from '@keeps-node-apis/@core';
import IEmployeeInfosRepository from './employee-infos.repository.interface';
import { EmployeeInfo } from '../../entities/employee-info.entity';
import { EmployeeInfoListParamsDto } from '../dtos/employee-info-list-params.dto';

const EMPLOYEE_INFO_TABLE = 'user_profile_workspace';

@Injectable()
export default class EmployeeInfosRepository extends Repository<EmployeeInfo> implements IEmployeeInfosRepository {
  constructor(dataSource: DataSource) {
    super(EmployeeInfo, dataSource.createEntityManager());
  }

  private addFilter(
    queryBuilder: SelectQueryBuilder<EmployeeInfo>,
    condition: string,
    parameters: Record<string, unknown>,
  ): SelectQueryBuilder<EmployeeInfo> {
    return queryBuilder.andWhere(condition, parameters);
  }

  createBaseQueryBuilder(): SelectQueryBuilder<EmployeeInfo> {
    return this.createQueryBuilder(EMPLOYEE_INFO_TABLE)
      .leftJoinAndMapOne(`${EMPLOYEE_INFO_TABLE}.user`, `${EMPLOYEE_INFO_TABLE}.user`, 'user')
      .leftJoinAndMapOne(`${EMPLOYEE_INFO_TABLE}.jobPosition`, `${EMPLOYEE_INFO_TABLE}.jobPosition`, 'jobPosition`')
      .leftJoinAndMapOne(`${EMPLOYEE_INFO_TABLE}.jobFunction`, `${EMPLOYEE_INFO_TABLE}.jobFunction`, 'jobFunction`')
      .where(`${EMPLOYEE_INFO_TABLE}.workspace_id = :workspaceId`);
  }

  private applyFilters(
    filters: EmployeeInfoListParamsDto,
    queryBuilder: SelectQueryBuilder<EmployeeInfo>,
  ): SelectQueryBuilder<EmployeeInfo> {
    const filterConditions: FilterCondition[] = [
      { condition: `${EMPLOYEE_INFO_TABLE}.user_id = :userId`, param: { userId: filters.userId }, key: 'userId' },
    ];

    filterConditions.forEach(({ condition, param, key }) => {
      if (filters[key]) {
        queryBuilder = this.addFilter(queryBuilder, condition, param);
      }
    });

    return queryBuilder;
  }

  findAllowed(workspaceId: string, filters?: EmployeeInfoListParamsDto): SelectQueryBuilder<EmployeeInfo> {
    const query = this.createBaseQueryBuilder()
      .setParameters({ workspaceId })
      .orderBy(`${EMPLOYEE_INFO_TABLE}.createdDate`, Order.DESC);
    if (filters) return this.applyFilters(filters, query);
    return query;
  }

  findUniqueValues(workspaceId: string, column: keyof EmployeeInfo, search?: string): SelectQueryBuilder<EmployeeInfo> {
    const columnMap: Partial<Record<keyof EmployeeInfo, string>> = {
      areaOfActivity: 'area_of_activity',
      manager: 'manager',
      director: 'director',
    };

    let databaseColumn = columnMap[column];
    if (!databaseColumn) {
      databaseColumn = column;
    }

    const query = this.createQueryBuilder(EMPLOYEE_INFO_TABLE)
      .select(`DISTINCT(${EMPLOYEE_INFO_TABLE}.${databaseColumn})`, column)
      .where(`${EMPLOYEE_INFO_TABLE}.workspace_id = :workspaceId`, { workspaceId })
      .andWhere(`${EMPLOYEE_INFO_TABLE}.${databaseColumn} is not null`)
      .andWhere(`${EMPLOYEE_INFO_TABLE}.${databaseColumn} != ''`);

    if (search)
      return query.andWhere(`UNACCENT(LOWER(${EMPLOYEE_INFO_TABLE}.${databaseColumn})) LIKE UNACCENT(LOWER(:search))`, {
        search: `%${search}%`,
      });
    return query;
  }
}

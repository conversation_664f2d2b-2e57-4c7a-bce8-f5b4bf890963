import { AwsModule, KeycloakModule, KpCacheModule, NotificationModule } from '@keeps-node-apis/@core';
import { Module } from '@nestjs/common';
import { EntitiesModule } from '../entities/entities.module';
import { JobModule } from '../job/job.module';
import { LanguagePreferencesController } from './controllers/language-preferences.controller';
import { UserRolesController } from './controllers/user-roles.controller';
import { UsersController } from './controllers/users.controller';
import { UsersRepository } from './repositories/users.repository';
import { EmployeeInfoService } from './services/employee-info.service';
import { UserRolesService } from './services/user-roles.service';
import { UsersService } from './services/users.service';
import UsersProviders from './users.providers';
import UserRolesRepository from './repositories/user-roles.repository';

@Module({
  imports: [KpCacheModule, KeycloakModule, AwsModule, NotificationModule, JobModule, EntitiesModule],
  controllers: [UsersController, UserRolesController, LanguagePreferencesController],
  providers: UsersProviders,
  exports: [UsersService, UserRolesService, EmployeeInfoService, UsersRepository, UserRolesRepository],
})
export class UsersModule {}

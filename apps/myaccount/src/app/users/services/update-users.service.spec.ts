import { Test, TestingModule } from '@nestjs/testing';
import { UpdateUsersService } from './update-users.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../../entities/user.entity';
import { EmployeeInfo } from '../../entities/employee-info.entity';
import { NotFoundException } from '@nestjs/common';
import { UpdateUserDto } from '../dtos/update-user.dto';
import { Repository } from 'typeorm';
import { Chance } from 'chance';
import { UpdateUserStatusDto } from '../dtos/update-user-status.dto';
import { AuthUser, KeycloakRepository } from '@keeps-node-apis/@core';
import { UsersRepository } from '../repositories/users.repository';
import { UserRolesTypeORMRepository } from '../repositories/user-roles-typeorm.repository';
import UserRolesRepository from '../repositories/user-roles.repository';
import { EmployeeInfoService } from './employee-info.service';

describe('UpdateUsersService', () => {
  let service: UpdateUsersService;
  let userRepositoryMock: Partial<Record<keyof UsersRepository, jest.Mock>>;
  let keycloakRepositoryMock: Partial<Record<keyof KeycloakRepository, jest.Mock>>;
  let employeeInfoRepositoryMock: Partial<Record<keyof Repository<EmployeeInfo>, jest.Mock>>;
  let userRolesRepositoryMock: Partial<jest.Mocked<UserRolesTypeORMRepository>>;
  let employeeInfoService: Partial<jest.Mock<EmployeeInfoService>>;
  const chance = new Chance();

  beforeEach(async () => {
    userRepositoryMock = {
      findByIdAndNotIntegration: jest.fn(),
      findByEmail: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
    };
    userRolesRepositoryMock = {
      userHasRoleOnWorkspace: jest.fn().mockResolvedValue(true),
    };

    keycloakRepositoryMock = {
      updateUser: jest.fn(),
    };

    employeeInfoRepositoryMock = {
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateUsersService,
        {
          provide: UsersRepository,
          useValue: userRepositoryMock,
        },
        {
          provide: KeycloakRepository,
          useValue: keycloakRepositoryMock,
        },
        {
          provide: getRepositoryToken(EmployeeInfo),
          useValue: employeeInfoRepositoryMock,
        },
        {
          provide: UserRolesRepository,
          useValue: userRolesRepositoryMock,
        },
        {
          provide: EmployeeInfoService,
          useValue: employeeInfoService,
        },
      ],
    }).compile();

    service = module.get<UpdateUsersService>(UpdateUsersService);
  });

  describe('update', () => {
    const userId = 'user-id';
    const workspaceId = 'workspace-id';
    const authenticatedUser: AuthUser = { sub: 'auth-user-id', roles: ['company_admin'] } as AuthUser;
    const existingUser: Partial<User> = { id: userId, name: 'Original Name', email: '<EMAIL>' };

    const updateUserDto: UpdateUserDto = {
      name: 'Updated Name',
    };

    it('should update a user successfully', async () => {
      userRepositoryMock.findByIdAndNotIntegration.mockResolvedValue(existingUser);
      userRepositoryMock.save.mockResolvedValue({ ...existingUser, ...updateUserDto });
      keycloakRepositoryMock.updateUser.mockResolvedValue(undefined);

      const result = await service.update(authenticatedUser, userId, updateUserDto, workspaceId);

      expect(userRepositoryMock.findByIdAndNotIntegration).toHaveBeenCalledWith(userId);
      expect(userRepositoryMock.save).toHaveBeenCalledWith(expect.objectContaining(updateUserDto));
      expect(keycloakRepositoryMock.updateUser).toHaveBeenCalled();
      expect(result).toEqual(expect.objectContaining(updateUserDto));
    });

    it('should throw NotFoundException if user does not exist', async () => {
      userRepositoryMock.findByIdAndNotIntegration.mockResolvedValue(null);

      await expect(service.update(authenticatedUser, userId, updateUserDto, workspaceId)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should handle leader assignment', async () => {
      const leaderEmail = '<EMAIL>';
      const leader: Partial<User> = { id: 'leader-id', email: leaderEmail };
      const dtoWithLeader: UpdateUserDto = { ...updateUserDto, relatedUserLeaderEmail: leaderEmail };

      userRepositoryMock.findByIdAndNotIntegration.mockResolvedValue(existingUser);
      userRepositoryMock.findByEmail.mockResolvedValue(leader);
      userRepositoryMock.save.mockResolvedValue({ ...existingUser, ...dtoWithLeader, relatedUserLeaderId: leader.id });
      keycloakRepositoryMock.updateUser.mockResolvedValue(undefined);

      const result = await service.update(authenticatedUser, userId, dtoWithLeader, workspaceId);

      expect(userRepositoryMock.findByEmail).toHaveBeenCalledWith(leaderEmail);
      expect(result.relatedUserLeaderId).toEqual(leader.id);
    });

    it('should throw NotFoundException if leader does not exist', async () => {
      const leaderEmail = '<EMAIL>';
      const dtoWithLeader: UpdateUserDto = { ...updateUserDto, relatedUserLeaderEmail: leaderEmail };

      userRepositoryMock.findByEmail.mockResolvedValue(null);

      await expect(service.update(authenticatedUser, userId, dtoWithLeader, workspaceId)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should sanitize EIN', async () => {
      const dtoWithEin: UpdateUserDto = { ...updateUserDto, ein: 12345.67 } as any;

      userRepositoryMock.findByIdAndNotIntegration.mockResolvedValue(existingUser);
      userRepositoryMock.save.mockResolvedValue({ ...existingUser, ...dtoWithEin, ein: '12345.67' });
      keycloakRepositoryMock.updateUser.mockResolvedValue(undefined);

      const result = await service.update(authenticatedUser, userId, dtoWithEin, workspaceId);
      expect(result.ein).toBe('12345.67');
    });

    it('should not update employee info if no data is provided', async () => {
      const existingUser: Partial<User> = { id: userId, name: 'Original Name', email: '<EMAIL>' };
      userRepositoryMock.findByIdAndNotIntegration.mockResolvedValue(existingUser);
      userRepositoryMock.save.mockResolvedValue({ ...existingUser, ...updateUserDto });
      keycloakRepositoryMock.updateUser.mockResolvedValue(undefined);

      const result = await service.update(authenticatedUser, userId, updateUserDto, workspaceId); // No employee info in DTO

      expect(employeeInfoRepositoryMock.findOne).not.toHaveBeenCalled();
      expect(employeeInfoRepositoryMock.create).not.toHaveBeenCalled();
      expect(employeeInfoRepositoryMock.save).not.toHaveBeenCalled();
      expect(result).not.toEqual(expect.objectContaining({ areaOfActivity: expect.anything() }));
    });

    it('should allow an user with the account_admin role to update its own account', async () => {
      const authenticatedUser: AuthUser = { sub: userId, roles: ['account_admin'] } as AuthUser;
      userRepositoryMock.findByIdAndNotIntegration.mockResolvedValue(existingUser);
      userRepositoryMock.save.mockResolvedValue({ ...existingUser, ...updateUserDto });
      keycloakRepositoryMock.updateUser.mockResolvedValue(undefined);

      await expect(service.update(authenticatedUser, userId, updateUserDto, workspaceId)).resolves.not.toThrow();
    });

    it('should throw when an unauthorized user tries to update its own account', async () => {
      const authenticatedUser: AuthUser = { sub: userId, roles: [] } as AuthUser;
      userRepositoryMock.findByIdAndNotIntegration.mockResolvedValue(existingUser);
      userRepositoryMock.save.mockResolvedValue({ ...existingUser, ...updateUserDto });
      keycloakRepositoryMock.updateUser.mockResolvedValue(undefined);

      await expect(service.update(authenticatedUser, userId, updateUserDto, workspaceId)).rejects.toThrow(
        'ROLE_NOT_FOUND',
      );
    });

    it('should throw when an unauthorized user tries to update another account', async () => {
      const authenticatedUser: AuthUser = { sub: 'mock-user-id', roles: [] } as AuthUser;
      userRepositoryMock.findByIdAndNotIntegration.mockResolvedValue(existingUser);
      userRepositoryMock.save.mockResolvedValue({ ...existingUser, ...updateUserDto });
      keycloakRepositoryMock.updateUser.mockResolvedValue(undefined);

      await expect(service.update(authenticatedUser, userId, updateUserDto, workspaceId)).rejects.toThrow(
        'ROLE_NOT_FOUND',
      );
    });
  });

  describe('updateUserStatus', () => {
    it('should throw an exception when the user tries to set its own status to false', async () => {
      const userId = chance.guid();
      const workspaceId = chance.guid();
      const updateUserStatusDto: UpdateUserStatusDto = { status: false };

      await expect(service.updateUserStatus(userId, userId, workspaceId, updateUserStatusDto)).rejects.toThrow(
        'CANNOT_DISABLE_YOUR_OWN_ACCOUNT',
      );
    });

    it('should update an existing user status', async () => {
      const user = { id: chance.guid(), status: false };
      const authenticatedUserId = chance.guid();
      const workspaceId = chance.guid();
      const updateUserStatusDto: UpdateUserStatusDto = { status: true };
      userRepositoryMock.findByIdAndNotIntegration.mockResolvedValueOnce(user);

      const result = await service.updateUserStatus(user.id, authenticatedUserId, workspaceId, updateUserStatusDto);

      expect(userRepositoryMock.findByIdAndNotIntegration).toHaveBeenCalledWith(user.id);
      expect(userRepositoryMock.update).toHaveBeenCalledWith(user.id, { status: true });
      expect(result).toEqual({ ...user, status: true });
    });
  });
});

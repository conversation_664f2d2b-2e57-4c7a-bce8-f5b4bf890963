import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EmployeeInfo } from '../../entities/employee-info.entity';
import { PageDto, PageOptionsWithSearchDto, paginate } from '@keeps-node-apis/@core';
import IEmployeeInfosRepository from '../repositories/employee-infos.repository.interface';
import { EmployeeInfoDto } from '../dtos/employee-info.dto';
import { EmployeeInfoBaseCreateDto } from '../dtos/employee-info-create.dto';
import { EmployeeInfoListParamsDto } from '../dtos/employee-info-list-params.dto';

@Injectable()
export class EmployeeInfoService {
  constructor(
    @InjectRepository(EmployeeInfo)
    private readonly repository: IEmployeeInfosRepository,
  ) {}

  async findAllowed(workspaceId: string, listParams: EmployeeInfoListParamsDto): Promise<PageDto<EmployeeInfoDto>> {
    const queryBuilder = this.repository.findAllowed(workspaceId, listParams);
    return await paginate<EmployeeInfo>(queryBuilder, listParams, 'user_profile_workspace');
  }

  async findUniqueValues(
    workspaceId: string,
    listParams: PageOptionsWithSearchDto,
    column: keyof EmployeeInfo,
  ): Promise<PageDto<string>> {
    const queryBuilder = this.repository.findUniqueValues(workspaceId, column, listParams?.search);
    const originalPage = await paginate(queryBuilder, listParams, 'user_profile_workspace', null, true);
    const managers = originalPage.items.map((item) => item[column]);
    return new PageDto(managers, listParams, originalPage.total) as PageDto<undefined>;
  }

  async upsert(userId: string, workspaceId: string, profileDto: EmployeeInfoBaseCreateDto): Promise<EmployeeInfoDto> {
    const employeeInfo = await this.findByUserAndWorkspace(userId, workspaceId);
    if (employeeInfo) {
      profileDto.id = employeeInfo.id;
      Object.assign(employeeInfo, { ...profileDto });
      return this.repository.save(employeeInfo);
    }

    const newEmployeeInfo = this.repository.create({ userId, workspaceId, ...profileDto });
    return this.repository.save(newEmployeeInfo);
  }

  async findByUserAndWorkspace(userId: string, workspaceId: string): Promise<EmployeeInfoDto | null> {
    return this.repository.findOneBy({
      userId,
      workspaceId,
    });
  }
}

/* eslint-disable @typescript-eslint/no-explicit-any */
import { Test, TestingModule } from '@nestjs/testing';
import { LanguagePreferencesService } from './language-preferences.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { LanguageDto } from '../dtos/language.dto';
import { Repository } from 'typeorm';
import { LanguagePreference } from '../../entities/language-preference.entity';

describe('LanguagePreferencesService', () => {
  let service: LanguagePreferencesService;
  let mockLanguagePreferencesRepository: jest.Mocked<Partial<Repository<LanguagePreference>>>;

  beforeEach(async () => {
    mockLanguagePreferencesRepository = {
      findBy: jest.fn(),
      findOneByOrFail: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LanguagePreferencesService,
        {
          provide: getRepositoryToken(LanguagePreference),
          useValue: mockLanguagePreferencesRepository,
        },
      ],
    }).compile();

    service = module.get<LanguagePreferencesService>(LanguagePreferencesService);
  });

  describe('findAll', () => {
    it('should retrieve and return all language preferences', async () => {
      const mockLanguages: LanguagePreference[] = [
        { id: '1', name: 'English' } as any,
        { id: '2', name: 'Spanish' } as any,
      ];

      const expectedLanguages: LanguageDto[] = mockLanguages;
      mockLanguagePreferencesRepository.findBy.mockResolvedValueOnce(mockLanguages);

      const result = await service.findAll();

      expect(result).toEqual(expectedLanguages);
      expect(mockLanguagePreferencesRepository.findBy).toHaveBeenCalledWith({ status: true });
    });
  });

  describe('findOneById', () => {
    it('should retrieve and return one', async () => {
      const languageId = '1';
      const mockLanguage: LanguagePreference = { id: '1', name: 'English' } as any;

      const expectedLanguage: LanguageDto = mockLanguage;
      mockLanguagePreferencesRepository.findOneByOrFail.mockResolvedValueOnce(mockLanguage);

      const result = await service.findOneById(languageId);

      expect(result).toEqual(expectedLanguage);
      expect(mockLanguagePreferencesRepository.findOneByOrFail).toHaveBeenCalledWith({ id: languageId, status: true });
    });
  });
});

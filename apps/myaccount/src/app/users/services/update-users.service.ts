import { Injectable, NotFoundException } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';

import { UpdateUserDto } from '../dtos/update-user.dto';
import { User } from '../../entities/user.entity';
import { convertPossibleFloatToString } from '../utils/convert-float.utils';
import { EmployeeInfo } from '../../entities/employee-info.entity';
import { UpdateUserStatusDto } from '../dtos/update-user-status.dto';
import { CannotDisableOwnAccountException } from '../exceptions/cannot-disable-own-account-exception';
import { AuthUser, KeycloakRepository, KeycloakUser, Locale } from '@keeps-node-apis/@core';
import { RoleNotFoundException } from '../exceptions/role-not-found.exception';
import { UsersRepository } from '../repositories/users.repository';
import UserRolesRepository from '../repositories/user-roles.repository';
import { EmployeeInfoService } from './employee-info.service';

@Injectable()
export class UpdateUsersService {
  constructor(
    private readonly userRepository: UsersRepository,
    private readonly keycloakRepository: KeycloakRepository,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly userRolesRepository: UserRolesRepository,
  ) {}

  async update(
    authenticatedUser: AuthUser,
    userId: string,
    data: UpdateUserDto,
    workspaceId: string,
  ): Promise<UpdateUserDto> {
    const updatedUserHasRoleWorkspace = await this.userRolesRepository.userHasRoleOnWorkspace(userId, workspaceId);
    const cannotUpdateUser = !this.canUpdateUser(authenticatedUser, userId) || !updatedUserHasRoleWorkspace;
    if (cannotUpdateUser) {
      throw new RoleNotFoundException();
    }

    const user = await this.getValidUser(userId);

    await this.handleLeaderAssignment(data);
    this.sanitizeEin(data);

    if (data.relatedUserLeaderEmail) {
      delete data.relatedUserLeaderEmail;
    }

    const updatedUser = await this.updateUserEntity(user, data);
    let employeeInfo = null;
    if (data.employeeInfo) {
      employeeInfo = await this.employeeInfoService.upsert(userId, workspaceId, data.employeeInfo);
    }

    await this.syncWithKeycloak(updatedUser);

    return this.buildResponseDto(updatedUser, employeeInfo);
  }

  async updateUserStatus(
    userId: string,
    authenticatedUserId: string,
    workspaceId: string,
    updateUserStatusDto: UpdateUserStatusDto,
  ): Promise<User> {
    const cannotUpdateUser = !(await this.userRolesRepository.userHasRoleOnWorkspace(userId, workspaceId));
    if (cannotUpdateUser) {
      throw new RoleNotFoundException();
    }

    const status = updateUserStatusDto.status;
    if (userId === authenticatedUserId && !status) {
      throw new CannotDisableOwnAccountException();
    }
    const user = await this.getValidUser(userId);
    await this.userRepository.update(userId, { status });
    user.status = status;
    return user;
  }

  private async getValidUser(userId: string): Promise<User> {
    const user = await this.userRepository.findByIdAndNotIntegration(userId);
    if (!user) throw new NotFoundException(`User ${userId} not found`);
    return user;
  }

  private async handleLeaderAssignment(data: UpdateUserDto): Promise<void> {
    if (!data.relatedUserLeaderEmail) return;

    const leader = await this.userRepository.findByEmail(data.relatedUserLeaderEmail);
    if (!leader) throw new NotFoundException(`Leader with email ${data.relatedUserLeaderEmail} not found`);

    data.relatedUserLeaderId = leader.id;
    delete data.relatedUserLeaderEmail;
  }

  private sanitizeEin(data: UpdateUserDto): void {
    if (data.ein) data.ein = convertPossibleFloatToString(data.ein);
  }

  private async updateUserEntity(user: User, data: UpdateUserDto): Promise<User> {
    const updatedUser = this.mergeUserData(user, data);
    return this.userRepository.save(updatedUser);
  }

  private mergeUserData(user: User, data: UpdateUserDto): User {
    return Object.assign(user, this.filterDefinedProperties(data));
  }

  private filterDefinedProperties(data: UpdateUserDto): Partial<User> {
    return Object.entries(data).reduce((acc, [key, value]) => {
      if (value !== null && value !== undefined) acc[key] = value;
      return acc;
    }, {});
  }

  private async syncWithKeycloak(user: User): Promise<void> {
    const keycloakUser = this.mapToKeycloakUser(user);
    await this.keycloakRepository.updateUser(user.id, keycloakUser);
  }

  private mapToKeycloakUser(user: User): KeycloakUser {
    const [firstName, lastName] = this.splitUserName(user.name);

    return {
      username: user.email,
      email: user.email,
      firstName,
      lastName,
      attributes: {
        locale: Locale[user.languageId],
      },
    };
  }

  private splitUserName(fullName: string): [string, string] {
    const [firstName = '', ...lastNameParts] = fullName?.split(' ') || [];
    return [firstName, lastNameParts.join(' ') || ''];
  }

  private buildResponseDto(user: User, employeeInfo?: EmployeeInfo): UpdateUserDto {
    const userDto = plainToInstance(UpdateUserDto, user);
    if (employeeInfo) {
      const { workspaceId, ...employeeInfoData } = employeeInfo;
      return plainToInstance(UpdateUserDto, {
        ...userDto,
        ...employeeInfoData,
      });
    }
    return userDto;
  }

  private canUpdateUser(authUser: AuthUser, userId: string) {
    const isCompanyAdmin = authUser.roles?.includes('company_admin');
    const isAccountAdmin = authUser.roles?.includes('account_admin');
    const canUpdateOwnUser = authUser.sub === userId && isAccountAdmin;
    return isCompanyAdmin || canUpdateOwnUser;
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { LanguagePreference } from '../../entities/language-preference.entity';
import { Repository } from 'typeorm';
import { LanguageDto } from '../dtos/language.dto';

@Injectable()
export class LanguagePreferencesService {
  constructor(
    @InjectRepository(LanguagePreference)
    private languagePreferencesRepository: Repository<LanguagePreference>,
  ) {}

  async findAll(): Promise<LanguageDto[]> {
    return await this.languagePreferencesRepository.findBy({ status: true });
  }

  async findOneById(id: string): Promise<LanguagePreference> {
    return this.languagePreferencesRepository.findOneByOrFail({ id, status: true });
  }
}

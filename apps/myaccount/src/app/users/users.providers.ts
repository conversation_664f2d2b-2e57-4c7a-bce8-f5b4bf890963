import { Provider } from '@nestjs/common';
import { getDataSourceToken, getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { EmployeeInfo } from '../entities/employee-info.entity';
import { ImageEditorService } from '../utils/image.service';
import EmployeeInfosRepository from './repositories/employee-infos.repository';
import { UserRolesTypeORMRepository } from './repositories/user-roles-typeorm.repository';
import { UsersTypeOrmRepository } from './repositories/users.repository-typeorm';
import { EmployeeInfoService } from './services/employee-info.service';
import { LanguagePreferencesService } from './services/language-preferences.service';
import { UpdateUsersService } from './services/update-users.service';
import { UserRolesService } from './services/user-roles.service';
import { UsersService } from './services/users.service';
import { UsersRepository } from './repositories/users.repository';
import UserRolesRepository from './repositories/user-roles.repository';

const REPOSITORIES: Provider[] = [
  {
    provide: UsersRepository,
    useClass: UsersTypeOrmRepository,
  },
  {
    provide: UserRolesRepository,
    useClass: UserRolesTypeORMRepository,
  },
  {
    provide: getRepositoryToken(EmployeeInfo),
    useFactory(datasource: DataSource) {
      return new EmployeeInfosRepository(datasource);
    },
    inject: [getDataSourceToken()],
  },
];

export default [
  ImageEditorService,
  EmployeeInfoService,
  UsersService,
  UserRolesService,
  LanguagePreferencesService,
  UpdateUsersService,
  ...REPOSITORIES,
];

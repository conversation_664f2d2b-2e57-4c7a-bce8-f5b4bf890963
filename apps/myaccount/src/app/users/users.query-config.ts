import { PaginateConfig, FilterOperator, FilterSuffix } from 'nestjs-paginate';
import { User } from '../entities/user.entity';

export const USER_PAGINATION_CONFIG: PaginateConfig<User> = {
  select: [
    // user
    'id',
    'name',
    'email',
    'phone',
    'status',
    'avatar',
    'relatedUserLeader.name',
    'relatedUserLeader.avatar',
    'language.name',
    'createdDate',
    // employeeInfos
    'employeeInfos.id',
    'employeeInfos.manager',
    'employeeInfos.director',
    'employeeInfos.areaOfActivity',
    'employeeInfos.jobPosition.name',
    'employeeInfos.jobFunction.name',
    'employeeInfos.workspaceId',
    // roles
    'roles.id',
    'roles.role.id',
    'roles.role.name',
    'roles.role.application.id',
    'roles.role.application.name',
    'roles.workspace.id',
    'roles.workspace.name',
  ],
  sortableColumns: ['name', 'email', 'createdDate'],
  searchableColumns: ['id', 'name', 'email'],
  defaultSortBy: [['createdDate', 'DESC']],
  filterableColumns: {
    id: [FilterOperator.IN],
    name: [FilterOperator.ILIKE],
    email: [FilterOperator.ILIKE],
    status: [FilterOperator.EQ, FilterSuffix.NOT],
    'employeeInfos.manager': [FilterOperator.EQ, FilterOperator.ILIKE],
    'employeeInfos.director': [FilterOperator.EQ, FilterOperator.ILIKE],
    'employeeInfos.areaOfActivity': [FilterOperator.EQ, FilterOperator.ILIKE],
    'language.name': [FilterOperator.EQ, FilterOperator.ILIKE],
    relatedUserLeaderId: [FilterOperator.IN],
    'employeeInfos.jobPositionId': [FilterOperator.IN],
    'employeeInfos.jobFunctionId': [FilterOperator.IN],
    'roles.role.id': [FilterOperator.IN],
  },
  relations: [
    'roles',
    'language',
    'roles.role.application',
    'roles.workspace',
    'relatedUserLeader',
    'employeeInfos',
    'employeeInfos.jobPosition',
    'employeeInfos.jobFunction',
  ],
  joinMethods: {
    relatedUserLeader: 'leftJoinAndSelect',
  },
};

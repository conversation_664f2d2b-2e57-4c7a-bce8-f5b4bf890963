import { BadRequestException, Injectable } from '@nestjs/common';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import * as fs from 'fs';
import { CONFIG_CONSTANTS } from '../config/constants';
import { S3Uploader } from '@keeps-node-apis/@core';
import 'multer';

@Injectable()
export class ImageEditorService {
  constructor(private readonly s3Uploader: S3Uploader) {}

  async cut(imageBuffer, width, height) {
    const tempFolder = './temp_uploads';

    if (!fs.existsSync(tempFolder)) {
      fs.mkdirSync(tempFolder);
    }

    const filename = `${uuidv4()}.png`;
    const outputPath = path.join(tempFolder, filename);

    await sharp(imageBuffer)
      .resize({
        width,
        height,
        fit: 'cover',
      })
      .toFile(outputPath);

    return outputPath;
  }

  async handleFileUpload(file: Express.Multer.File, folder: string, mimeType: string, width = 500, height = 500) {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    if (!CONFIG_CONSTANTS.ALLOWED_IMAGES[file.mimetype]) {
      throw new BadRequestException('Unsupported image type');
    }

    const cutImagePath = await this.cut(file.buffer, width, height);
    const extension = CONFIG_CONSTANTS.ALLOWED_IMAGES[file.mimetype];
    const fileName = `${uuidv4()}.${extension}`;

    const uploadResponse = await this.s3Uploader.uploadFilePath(cutImagePath, `${folder}/${fileName}`, mimeType);
    return uploadResponse;
  }
}

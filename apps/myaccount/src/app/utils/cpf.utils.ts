export function isValidCPF(cpf: string): boolean {
  if (typeof cpf !== 'string') return false;
  cpf = cpf.replace(/[^\d]+/g, '');
  if (cpf.length !== 11 || !!cpf.match(/(\d)\1{10}/)) return false;

  const cpfArray = cpf.split('').map((el) => +el);
  const rest = (count: number) =>
    ((cpfArray.slice(0, count - 12).reduce((sum, el, index) => sum + el * (count - index), 0) * 10) % 11) % 10;

  return rest(10) === cpfArray[9] && rest(11) === cpfArray[10];
}

export function formatCPF(cpf: string): string {
  if (!cpf) return '';
  cpf = cpf.replace(/[^\d]+/g, '');
  return cpf.padStart(11, '0');
}

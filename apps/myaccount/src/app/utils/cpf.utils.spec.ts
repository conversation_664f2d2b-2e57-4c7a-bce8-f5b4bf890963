import { formatCPF, isValidCPF } from './cpf.utils';

describe('CPF Utils', () => {
  describe('isValidCPF', () => {
    it('should return true for a valid CPF', () => {
      expect(isValidCPF('529.982.247-25')).toBe(true);
      expect(isValidCPF('52998224725')).toBe(true);
    });

    it('should return false for a CPF with all digits equal', () => {
      expect(isValidCPF('111.111.111-11')).toBe(false);
      expect(isValidCPF('22222222222')).toBe(false);
    });

    it('should return false for a CPF with incorrect length', () => {
      expect(isValidCPF('123')).toBe(false);
      expect(isValidCPF('123456789012345')).toBe(false);
    });

    it('should return false for a CPF with non-numeric characters', () => {
      expect(isValidCPF('ABC.DEF.GHI-JK')).toBe(false);
    });

    it('should return false for an empty string', () => {
      expect(isValidCPF('')).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(isValidCPF(null as any)).toBe(false);
      expect(isValidCPF(undefined as any)).toBe(false);
    });

    it('should return false for non-string input', () => {
      expect(isValidCPF(12345678909 as any)).toBe(false);
      expect(isValidCPF({} as any)).toBe(false);
    });
  });

  describe('formatCPF', () => {
    it('should format a CPF with 11 digits', () => {
      expect(formatCPF('52998224725')).toBe('52998224725');
      expect(formatCPF('529.982.247-25')).toBe('52998224725');
    });

    it('should pad a CPF with less than 11 digits', () => {
      expect(formatCPF('123')).toBe('00000000123');
      expect(formatCPF('1')).toBe('00000000001');
    });

    it('should handle CPF with leading zeros', () => {
      expect(formatCPF('00123456789')).toBe('00123456789');
    });

    it('should remove non-numeric characters', () => {
      expect(formatCPF('529.982.247-25')).toBe('52998224725');
      expect(formatCPF('529@982#247$25')).toBe('52998224725');
    });

    it('should return an empty string for empty input', () => {
      expect(formatCPF('')).toBe('');
    });

    it('should handle null or undefined', () => {
      expect(formatCPF(null as any)).toBe('');
      expect(formatCPF(undefined as any)).toBe('');
    });
  });
});

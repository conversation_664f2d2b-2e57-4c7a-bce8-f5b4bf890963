import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { Workspace } from '../../entities/workspace.entity';
import IWorkspacesRepository from './workspaces-repository.interface';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import { Service } from '../../entities/service.entity';

@Injectable()
export class WorkspacesRepository extends Repository<Workspace> implements IWorkspacesRepository {
  private readonly dataSource: DataSource;

  constructor(dataSource: DataSource) {
    super(Workspace, dataSource.createEntityManager());
    this.dataSource = dataSource;
  }

  findAllUserWorkspaces(userId: string): Promise<Workspace[]> {
    const queryWorkspaceIds = this.dataSource
      .getRepository(UserRoleWorkspace)
      .createQueryBuilder()
      .select('workspace_id')
      .where('user_id = :userId', { userId });

    const queryBuilder = this.createQueryBuilder('workspace')
      .where('workspace.id in (' + queryWorkspaceIds.getQuery() + ')')
      .setParameters(queryWorkspaceIds.getParameters());
    return queryBuilder.getMany();
  }

  getWorkspacesByApplicationAndUser(userId: string, applicationId: string) {
    return this.createQueryBuilder('ws')
      .innerJoin('user_role_workspace', 'urw', 'urw.workspace_id = ws.id')
      .innerJoin('role', 'r', 'urw.role_id = r.id')
      .innerJoin('application', 'app', 'r.application_id = app.id')
      .innerJoin('service_workspace', 'sw', 'sw.workspace_id = ws.id')
      .leftJoinAndMapMany('ws.services', Service, 's', 's.id = sw.service_id')
      .where('urw.userId = :userId', { userId })
      .andWhere('urw.status = true')
      .andWhere('app.id = :applicationId', { applicationId })
      .getMany();
  }
}

import { CryptoService } from '@keeps-node-apis/@core';
import { Provider } from '@nestjs/common';
import { getDataSourceToken, getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { Workspace } from '../entities/workspace.entity';
import { ImageEditorService } from '../utils/image.service';
import { WorkspacesRepository } from './repositories/workspaces-repository';
import { WorkspacesService } from './services/workspaces.service';
import { WorkspaceIntegrationsSubscriber } from './workspace-integrations.subscriber';
import { WorkspaceCustomMenuItemsService } from './services/workspace-custom-menu-items.service';

export const WORKSPACES_REPOSITORY: Provider = {
  provide: getRepositoryToken(Workspace),
  useFactory(datasource: DataSource) {
    return new WorkspacesRepository(datasource);
  },
  inject: [getDataSourceToken()],
};

export default [
  WorkspacesService,
  WorkspaceIntegrationsSubscriber,
  WORKSPACES_REPOSITORY,
  ImageEditorService,
  CryptoService,
  WorkspaceCustomMenuItemsService,
];

import { Auth<PERSON><PERSON>, MYACCOUNT_ADMIN_ROLES, <PERSON><PERSON>, Serialize, SkipTenant } from '@keeps-node-apis/@core';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';

import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthenticatedUser, Public } from 'nest-keycloak-connect';
import { ServiceWorkspace } from '../../entities/service-workspace.entity';
import { ImageEditorService } from '../../utils/image.service';
import { CreateWorkspaceDto } from '../dto/create-workspace.dto';
import { UpdateWorkspaceDto } from '../dto/update-workspace.dto';
import { WorkspaceCustomLoginUrlDto } from '../dto/workspace-custom-login-url.dto';
import { WorkspaceListDto } from '../dto/workspace-list.dto';
import { WorkspaceThemeIdDto } from '../dto/workspace-theme-id.dto';
import { WorkspaceDto } from '../dto/workspace.dto';
import { WorkspacesService } from '../services/workspaces.service';
import { ServiceWorkspaceDto } from '../dto/service-workspace.dto';
import { WorkspaceBannerModeDto } from '../dto/workspace-banner-mode.dto';
import { WorkspaceBannerModeResponseDto } from '../dto/workspace-banner-mode-response.dto';
import { WorkspaceCustomMenuItemsService } from '../services/workspace-custom-menu-items.service';
import { CreateWorkspaceCustomMenuItemDto } from '../dto/create-workspace-custom-menu-item.dto';
import { WorkspaceCustomMenuItemDto } from '../dto/workspace-custom-menu-item.dto';

/**
 * Controller for managing workspace-related operations.
 */
@ApiTags('workspaces')
@Controller('workspaces')
export class WorkspacesController {
  constructor(
    private readonly workspacesService: WorkspacesService,
    private readonly imageEditorService: ImageEditorService,
    private readonly workspaceCustomMenuItemsService: WorkspaceCustomMenuItemsService,
  ) {}

  /**
   * Creates a new workspace.
   *
   * @param {CreateWorkspaceDto} createWorkspaceDto - The data for the new workspace.
   * @param {AuthUser} user - The authenticated user creating the workspace.
   * @returns {Promise<WorkspaceDto>} The created workspace.
   */
  @Post()
  @ApiOperation({ summary: 'Create a new workspace' })
  @SkipTenant()
  @Roles(['realm_access.keeps_platform_admin'])
  @ApiBody({ type: CreateWorkspaceDto })
  @ApiResponse({ status: 201, description: 'Workspace created successfully', type: WorkspaceDto })
  create(@Body() createWorkspaceDto: CreateWorkspaceDto, @AuthenticatedUser() user: AuthUser) {
    return this.workspacesService.create(createWorkspaceDto, user.sub);
  }

  /**
   * Retrieves all workspaces the authenticated user is allowed to access.
   *
   * @param {AuthUser} user - The authenticated user.
   * @returns {Promise<WorkspaceListDto[]>} A list of accessible workspaces.
   */
  @Get()
  @SkipTenant()
  @ApiOperation({ summary: 'Get all accessible workspaces' })
  @ApiResponse({ status: 200, description: 'List of workspaces', type: WorkspaceListDto, isArray: true })
  @Serialize(WorkspaceDto)
  async findAll(@AuthenticatedUser() user: AuthUser): Promise<WorkspaceListDto[]> {
    return this.workspacesService.findAllowed(user.sub);
  }

  /**
   * Retrieves a single workspace by its ID.
   *
   * @param {string} id - The ID of the workspace.
   * @returns {Promise<WorkspaceDto>} The workspace details.
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get a workspace by ID' })
  @ApiParam({ name: 'id', description: 'The ID of the workspace', type: String })
  @ApiResponse({ status: 200, description: 'Workspace details', type: WorkspaceDto })
  @Serialize(WorkspaceDto)
  findOne(@Param('id') id: string) {
    return this.workspacesService.findOne(id);
  }

  /**
   * Retrieves the theme ID of a workspace.
   *
   * @param {string} id - The ID of the workspace.
   * @returns {Promise<WorkspaceThemeIdDto>} The workspace theme ID.
   */
  @Get(':id/theme')
  @ApiOperation({ summary: 'Get the theme ID of a workspace' })
  @ApiParam({ name: 'id', description: 'The ID of the workspace', type: String })
  @ApiResponse({ status: 200, description: 'Workspace theme ID', type: WorkspaceThemeIdDto })
  getWorkspaceTheme(@Param('id') id: string) {
    return this.workspacesService.findWorkspaceThemeId(id);
  }

  @Post(':id/services/:serviceId')
  @ApiOperation({ summary: 'Add a service to a workspace' })
  @ApiParam({ name: 'id', description: 'The ID of the workspace', type: String })
  @ApiParam({ name: 'serviceId', description: 'The ID of the service', type: String })
  @ApiResponse({ status: 201, description: 'Service added to workspace', type: ServiceWorkspace })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  @Serialize(ServiceWorkspaceDto)
  createServices(@Param('id') workspaceId: string, @Param('serviceId') serviceId: string) {
    return this.workspacesService.addServiceInWorkspace(workspaceId, serviceId);
  }

  /**
   * Soft deletes a service from a workspace by setting status to false.
   *
   * @param {string} workspaceId - The ID of the workspace.
   * @param {string} serviceId - The ID of the service to remove.
   * @returns {Promise<void>} Nothing is returned on success.
   */
  @Delete(':id/services/:serviceId')
  @ApiOperation({ summary: 'Soft delete a service from a workspace' })
  @ApiParam({ name: 'id', description: 'The ID of the workspace', type: String })
  @ApiParam({ name: 'serviceId', description: 'The ID of the service', type: String })
  @ApiResponse({ status: 204, description: 'Service soft deleted from workspace successfully' })
  removeService(@Param('id') workspaceId: string, @Param('serviceId') serviceId: string) {
    return this.workspacesService.removeServiceFromWorkspace(workspaceId, serviceId);
  }

  /**
   * Updates a workspace.
   *
   * @param {string} id - The ID of the workspace.
   * @param {UpdateWorkspaceDto} updateWorkspaceDto - The updated workspace data.
   * @returns {Promise<WorkspaceDto>} The updated workspace.
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update a workspace' })
  @ApiParam({ name: 'id', description: 'The ID of the workspace', type: String })
  @ApiBody({ type: UpdateWorkspaceDto })
  @ApiResponse({ status: 200, description: 'Workspace updated successfully', type: WorkspaceDto })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  update(@Param('id') id: string, @Body() updateWorkspaceDto: UpdateWorkspaceDto) {
    return this.workspacesService.update(id, updateWorkspaceDto);
  }

  /**
   * Deletes a workspace.
   *
   * @param {string} id - The ID of the workspace.
   * @returns {Promise<void>} Nothing is returned on success.
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a workspace' })
  @ApiParam({ name: 'id', description: 'The ID of the workspace', type: String })
  @ApiResponse({ status: 204, description: 'Workspace deleted successfully' })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  remove(@Param('id') id: string) {
    return this.workspacesService.remove(id);
  }

  /**
   * Uploads a logo for a workspace.
   *
   * @param {Express.Multer.File} file - The logo file to upload.
   * @returns {Promise<{ statusCode: number; data: any }>} The upload response.
   */
  @Post('logo')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a workspace logo' })
  @ApiResponse({ status: 200, description: 'Logo uploaded successfully' })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  async uploadLogo(@UploadedFile() file: Express.Multer.File) {
    const uploadResponse = await this.imageEditorService.handleFileUpload(file, 'workspace-logo', 'image/png');
    return { statusCode: HttpStatus.OK, data: uploadResponse };
  }

  /**
   * Uploads an icon for a workspace.
   *
   * @param {Express.Multer.File} file - The icon file to upload.
   * @returns {Promise<{ statusCode: number; data: any }>} The upload response.
   */
  @Post('icon')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a workspace icon' })
  @ApiResponse({ status: 200, description: 'Icon uploaded successfully' })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  async uploadIcon(@UploadedFile() file: Express.Multer.File) {
    const uploadResponse = await this.imageEditorService.handleFileUpload(file, 'workspace-icon', 'image/png');
    return { statusCode: HttpStatus.OK, data: uploadResponse };
  }

  /**
   * Uploads an SVG icon for a workspace.
   *
   * @param {Express.Multer.File} file - The SVG icon file to upload.
   * @returns {Promise<{ statusCode: number; data: any }>} The upload response.
   */
  @Post('icon-svg')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a workspace SVG icon' })
  @ApiResponse({ status: 200, description: 'SVG icon uploaded successfully' })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  async uploadIconSvg(@UploadedFile() file: Express.Multer.File) {
    const uploadResponse = await this.imageEditorService.handleFileUpload(file, 'workspace-svg', 'image/svg+xml');
    return { statusCode: HttpStatus.OK, data: uploadResponse };
  }

  /**
   * Retrieves a custom login URL by workspace hash ID (public endpoint).
   *
   * @param {string} hashId - The hash ID of the workspace.
   * @returns {Promise<WorkspaceCustomLoginUrlDto>} The custom login URL.
   */
  @Public()
  @SkipTenant()
  @Get('hash-login-url/:hashId/')
  @ApiOperation({ summary: 'Get custom login URL by workspace hash ID' })
  @ApiParam({ name: 'hashId', description: 'The hash ID of the workspace', type: String })
  @ApiResponse({ status: 200, description: 'Custom login URL', type: WorkspaceCustomLoginUrlDto })
  async getLoginUrl(@Param('hashId') hashId: string) {
    return await this.workspacesService.findLoginUrlByHashId(hashId);
  }

  /**
   * Retrieves the banner mode for a workspace.
   *
   * @param {string} id - The ID of the workspace.
   * @returns {Promise<WorkspaceBannerModeResponseDto>} The workspace banner mode.
   */
  @Get(':id/banner-mode')
  @ApiOperation({ summary: 'Get the banner mode for a workspace' })
  @ApiParam({ name: 'id', description: 'The ID of the workspace', type: String })
  @ApiResponse({ status: 200, description: 'Banner mode retrieved successfully', type: WorkspaceBannerModeResponseDto })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  getBannerMode(@Param('id') id: string) {
    return this.workspacesService.getBannerMode(id);
  }

  /**
   * Updates the banner mode for a workspace.
   *
   * @param {string} id - The ID of the workspace.
   * @param {WorkspaceBannerModeDto} bannerModeDto - The banner mode data.
   * @returns {Promise<void>} Nothing is returned on success.
   */
  @Post(':id/banner-mode')
  @ApiOperation({ summary: 'Update the banner mode for a workspace' })
  @ApiParam({ name: 'id', description: 'The ID of the workspace', type: String })
  @ApiBody({ type: WorkspaceBannerModeDto })
  @ApiResponse({ status: 204, description: 'Banner mode updated successfully' })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  async updateBannerMode(@Param('id') id: string, @Body() bannerModeDto: WorkspaceBannerModeDto) {
    await this.workspacesService.updateBannerMode(id, bannerModeDto);
  }

  @Post('custom-menu-items')
  @ApiOperation({ summary: 'Create a new workspace custom menu item' })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  @ApiBody({ type: CreateWorkspaceCustomMenuItemDto })
  @ApiResponse({ status: 201, description: 'Custom menu item created successfully', type: WorkspaceCustomMenuItemDto })
  @Serialize(WorkspaceCustomMenuItemDto)
  createCustomMenuItem(@Body() createWorkspaceCustomMenuItemDto: CreateWorkspaceCustomMenuItemDto) {
    return this.workspaceCustomMenuItemsService.create(createWorkspaceCustomMenuItemDto);
  }
}

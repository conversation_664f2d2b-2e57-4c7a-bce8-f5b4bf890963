import { PickType } from '@nestjs/mapped-types';
import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { RoleDto } from '../../users/dtos/role.dto';

/**
 * Data Transfer Object representing a slim version of a role.
 */
export class RoleSlimDto extends PickType(RoleDto, [] as const) {
  @ApiProperty({ type: String, description: 'The ID of the application this role belongs to' })
  @Expose()
  applicationId: string;

  @ApiProperty({ type: String, description: 'The unique identifier of the role' })
  @Expose()
  id: string;

  @ApiProperty({ type: String, description: 'A unique key for the role' })
  @Expose()
  key: string;

  @ApiProperty({ type: String, description: 'The name of the role' })
  @Expose()
  name: string;
}

/**
 * Data Transfer Object representing an application with its associated roles.
 */
export class ApplicationRoleListDto {
  @ApiProperty({ type: String, description: 'The unique identifier of the application' })
  @Expose()
  id: string;

  @ApiProperty({ type: RoleSlimDto, isArray: true, description: 'The list of roles associated with the application' })
  @Expose()
  @Type(() => RoleSlimDto)
  roles: RoleSlimDto[];

  @ApiProperty({ type: String, description: 'The name of the application' })
  @Expose()
  name: string;

  @ApiProperty({ type: String, required: false, description: 'A description of the application' })
  @Expose()
  description: string;

  @ApiProperty({ type: Boolean, description: 'The status of the application (active or inactive)' })
  @Expose()
  status: boolean;
}

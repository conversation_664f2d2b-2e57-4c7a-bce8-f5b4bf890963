import { validate } from 'class-validator';
import { WorkspaceBannerModeDto } from './workspace-banner-mode.dto';
import { BannerMode } from '../types/banner-mode.enum';

describe('WorkspaceBannerModeDto', () => {
  describe('validation', () => {
    it('should pass validation with valid MANUAL banner mode', async () => {
      const dto = new WorkspaceBannerModeDto();
      dto.bannerMode = BannerMode.MANUAL;

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with valid RECOMMENDATION banner mode', async () => {
      const dto = new WorkspaceBannerModeDto();
      dto.bannerMode = BannerMode.RECOMMENDATION;

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation when bannerMode is empty', async () => {
      const dto = new WorkspaceBannerModeDto();
      // @ts-ignore - intentionally setting invalid value for testing
      dto.bannerMode = '';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('bannerMode');
      expect(errors[0].constraints).toHaveProperty('isNotEmpty');
    });

    it('should fail validation when bannerMode is null', async () => {
      const dto = new WorkspaceBannerModeDto();
      dto.bannerMode = null;

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('bannerMode');
      expect(errors[0].constraints).toHaveProperty('isNotEmpty');
    });

    it('should fail validation when bannerMode is undefined', async () => {
      const dto = new WorkspaceBannerModeDto();
      // bannerMode is undefined by default

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('bannerMode');
      expect(errors[0].constraints).toHaveProperty('isNotEmpty');
    });

    it('should fail validation with invalid enum value', async () => {
      const dto = new WorkspaceBannerModeDto();
      // @ts-ignore - intentionally setting invalid value for testing
      dto.bannerMode = 'INVALID_MODE';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('bannerMode');
      expect(errors[0].constraints).toHaveProperty('isEnum');
    });

    it('should fail validation with numeric value', async () => {
      const dto = new WorkspaceBannerModeDto();
      // @ts-ignore - intentionally setting invalid value for testing
      dto.bannerMode = 123;

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('bannerMode');
      expect(errors[0].constraints).toHaveProperty('isEnum');
    });

    it('should fail validation with boolean value', async () => {
      const dto = new WorkspaceBannerModeDto();
      // @ts-ignore - intentionally setting invalid value for testing
      dto.bannerMode = true;

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('bannerMode');
      expect(errors[0].constraints).toHaveProperty('isEnum');
    });
  });
});

import {
  AwsModule,
  KeycloakModule,
  KpCacheModule,
  NotificationModule,
  MobileNotificationsModule,
} from '@keeps-node-apis/@core';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersCreatorController } from './controllers/users-creator.controller';
import { JobFunction } from '../entities/job-function.entity';
import { LanguagePreference } from '../entities/language-preference.entity';
import { Role } from '../entities/role.entity';
import { UserRoleWorkspace } from '../entities/user-role-workspace.entity';
import { User } from '../entities/user.entity';
import { Job } from '../entities/job.entity';
import { ServiceWorkspace } from '../entities/service-workspace.entity';
import { UserGrpcController } from './controllers/create-users-grpc.controller';
import { JobModule } from '../job/job.module';
import usersCreatorProviders, { EXPORT_REPOSITORY } from './users-creator.providers';
import { UsersModule } from '../users/users.module';
import { IdpWorkspace } from '../entities/idp-workspace.entity';

@Module({
  imports: [
    KpCacheModule,
    KeycloakModule,
    TypeOrmModule.forFeature([
      User,
      UserRoleWorkspace,
      Role,
      LanguagePreference,
      JobFunction,
      Job,
      ServiceWorkspace,
      IdpWorkspace,
    ]),
    AwsModule,
    NotificationModule,
    JobModule,
    UsersModule,
    MobileNotificationsModule,
  ],
  controllers: [UserGrpcController, UsersCreatorController],
  providers: usersCreatorProviders,
  exports: [...EXPORT_REPOSITORY],
})
export class UsersCreatorModule {}
